// 物品管理器 - 管理點數和大力丸

import { gameState, GAME_CONFIG, getRandomElement } from './gameState.js';

export class ItemManager {
    constructor(scene) {
        this.scene = scene;
        this.dots = [];
        this.powerPellets = [];
        this.itemGroup = null;
        
        this.setupItemGroups();
    }
    
    setupItemGroups() {
        // 建立物品群組以便管理
        this.itemGroup = this.scene.add.group();
    }
    
    generateItems() {
        console.log('生成遊戲物品');
        
        // 清理現有物品
        this.clearAllItems();
        
        // 生成點數和大力丸
        this.generateDots();
        this.generatePowerPellets();
        
        // 更新遊戲狀態
        gameState.dots = this.dots;
        gameState.powerPellets = this.powerPellets;
        gameState.totalDots = this.dots.length + this.powerPellets.length;
        
        console.log(`物品生成完成 - 點數: ${this.dots.length}, 大力丸: ${this.powerPellets.length}`);
    }
    
    generateDots() {
        const mapManager = this.scene.mapManager;
        if (!mapManager || mapManager.validPositions.length === 0) {
            console.warn('無法生成點數：沒有有效位置');
            return;
        }
        
        // 計算要生成的點數數量 (基於地圖大小)
        const totalPositions = mapManager.validPositions.length;
        const dotCount = Math.min(Math.floor(totalPositions * 0.1), 100); // 減少點數數量，最多 100 個

        console.log(`準備生成 ${dotCount} 個點數，總位置數: ${totalPositions}`);

        // 使用網格採樣來更均勻分佈點數
        const selectedPositions = [];
        const gridSize = Math.ceil(Math.sqrt(dotCount));
        const stepSize = Math.floor(totalPositions / (gridSize * gridSize));

        for (let i = 0; i < totalPositions && selectedPositions.length < dotCount; i += stepSize) {
            if (i < mapManager.validPositions.length) {
                selectedPositions.push(mapManager.validPositions[i]);
            }
        }

        // 如果還需要更多點數，隨機添加
        const usedIndices = new Set();
        for (let i = 0; i < selectedPositions.length; i++) {
            usedIndices.add(i * stepSize);
        }

        while (selectedPositions.length < dotCount && usedIndices.size < totalPositions) {
            const randomIndex = Math.floor(Math.random() * totalPositions);
            if (!usedIndices.has(randomIndex)) {
                selectedPositions.push(mapManager.validPositions[randomIndex]);
                usedIndices.add(randomIndex);
            }
        }
        
        // 建立點數精靈
        selectedPositions.forEach((position, index) => {
            const dot = this.createDot(position[0], position[1], index);
            this.dots.push(dot);
        });

        console.log(`實際生成了 ${this.dots.length} 個點數`);
    }
    
    generatePowerPellets() {
        const mapManager = this.scene.mapManager;
        if (!mapManager || mapManager.validPositions.length === 0) {
            console.warn('無法生成大力丸：沒有有效位置');
            return;
        }
        
        // 生成 4 個大力丸 (經典配置)
        const pelletCount = 4;
        const usedPositions = new Set();
        
        // 避免與點數重疊
        this.dots.forEach(dot => {
            const posKey = `${dot.lat.toFixed(6)},${dot.lng.toFixed(6)}`;
            usedPositions.add(posKey);
        });
        
        const selectedPositions = [];
        let attempts = 0;
        const maxAttempts = 100;
        
        while (selectedPositions.length < pelletCount && attempts < maxAttempts) {
            const randomPos = getRandomElement(mapManager.validPositions);
            const posKey = `${randomPos[0].toFixed(6)},${randomPos[1].toFixed(6)}`;
            
            if (!usedPositions.has(posKey)) {
                selectedPositions.push(randomPos);
                usedPositions.add(posKey);
            }
            attempts++;
        }
        
        // 建立大力丸精靈
        selectedPositions.forEach((position, index) => {
            const pellet = this.createPowerPellet(position[0], position[1], index);
            this.powerPellets.push(pellet);
        });
    }
    
    createDot(lat, lng, id) {
        const mapManager = this.scene.scene.get('GameScene').mapManager;
        const pixelPos = mapManager.latLngToPixel(lat, lng);

        console.log(`建立點數 ${id}: 經緯度(${lat.toFixed(6)}, ${lng.toFixed(6)}) -> 像素(${pixelPos.x}, ${pixelPos.y})`);

        const dot = this.scene.add.sprite(pixelPos.x, pixelPos.y, 'dot');
        dot.setOrigin(0.5, 0.5);
        dot.setDepth(1);
        dot.setScale(2); // 放大點數讓它更明顯

        // 添加屬性
        dot.id = id;
        dot.lat = lat;
        dot.lng = lng;
        dot.type = 'dot';
        dot.points = GAME_CONFIG.DOT_POINTS;

        // 添加到群組
        this.itemGroup.add(dot);

        // 添加微妙的閃爍效果
        this.scene.tweens.add({
            targets: dot,
            alpha: 0.7,
            duration: 1000 + Math.random() * 1000,
            ease: 'Sine.easeInOut',
            yoyo: true,
            repeat: -1
        });

        return dot;
    }
    
    createPowerPellet(lat, lng, id) {
        const mapManager = this.scene.scene.get('GameScene').mapManager;
        const pixelPos = mapManager.latLngToPixel(lat, lng);
        
        const pellet = this.scene.add.sprite(pixelPos.x, pixelPos.y, 'power-pellet');
        pellet.setOrigin(0.5, 0.5);
        pellet.setDepth(1);
        
        // 添加屬性
        pellet.id = id;
        pellet.lat = lat;
        pellet.lng = lng;
        pellet.type = 'power-pellet';
        pellet.points = GAME_CONFIG.POWER_PELLET_POINTS;
        
        // 添加到群組
        this.itemGroup.add(pellet);
        
        // 添加明顯的閃爍效果
        this.scene.tweens.add({
            targets: pellet,
            alpha: 0.3,
            scale: 0.8,
            duration: 500,
            ease: 'Sine.easeInOut',
            yoyo: true,
            repeat: -1
        });
        
        return pellet;
    }
    
    updateItemPositions() {
        // 當地圖變化時更新所有物品位置
        const mapManager = this.scene.mapManager;
        if (!mapManager) return;
        
        this.dots.forEach(dot => {
            if (dot.active) {
                const pixelPos = mapManager.latLngToPixel(dot.lat, dot.lng);
                dot.setPosition(pixelPos.x, pixelPos.y);
            }
        });
        
        this.powerPellets.forEach(pellet => {
            if (pellet.active) {
                const pixelPos = mapManager.latLngToPixel(pellet.lat, pellet.lng);
                pellet.setPosition(pixelPos.x, pixelPos.y);
            }
        });
    }
    
    collectDot(dot) {
        if (!dot.active) return false;
        
        // 播放收集效果
        this.playCollectEffect(dot);
        
        // 隱藏點數
        dot.setActive(false).setVisible(false);
        
        return true;
    }
    
    collectPowerPellet(pellet) {
        if (!pellet.active) return false;
        
        // 播放收集效果
        this.playCollectEffect(pellet, true);
        
        // 隱藏大力丸
        pellet.setActive(false).setVisible(false);
        
        return true;
    }
    
    playCollectEffect(item, isPowerPellet = false) {
        // 建立收集特效
        const effect = this.scene.add.circle(item.x, item.y, isPowerPellet ? 15 : 8, 0xFFFF00);
        effect.setDepth(20);
        effect.setAlpha(0.8);
        
        // 特效動畫
        this.scene.tweens.add({
            targets: effect,
            scale: isPowerPellet ? 3 : 2,
            alpha: 0,
            duration: isPowerPellet ? 400 : 200,
            ease: 'Power2',
            onComplete: () => {
                effect.destroy();
            }
        });
        
        // 分數文字特效
        const scoreText = this.scene.add.text(
            item.x, 
            item.y - 20, 
            `+${item.points}`, 
            {
                fontSize: isPowerPellet ? '16px' : '12px',
                fill: '#FFFF00',
                fontFamily: 'Cubic 11, cursive',
                stroke: '#000000',
                strokeThickness: 2
            }
        );
        scoreText.setOrigin(0.5, 0.5);
        scoreText.setDepth(21);
        
        this.scene.tweens.add({
            targets: scoreText,
            y: scoreText.y - 30,
            alpha: 0,
            duration: 800,
            ease: 'Power2',
            onComplete: () => {
                scoreText.destroy();
            }
        });
    }
    
    getActiveDotsCount() {
        return this.dots.filter(dot => dot.active).length;
    }
    
    getActivePelletsCount() {
        return this.powerPellets.filter(pellet => pellet.active).length;
    }
    
    getAllActiveItems() {
        return [
            ...this.dots.filter(dot => dot.active),
            ...this.powerPellets.filter(pellet => pellet.active)
        ];
    }
    
    update(time, delta) {
        // 更新物品位置 (如果需要)
        // 這裡可以添加物品的動態效果
    }
    
    clearAllItems() {
        // 清理所有點數
        this.dots.forEach(dot => {
            if (dot && dot.destroy) {
                dot.destroy();
            }
        });
        this.dots = [];
        
        // 清理所有大力丸
        this.powerPellets.forEach(pellet => {
            if (pellet && pellet.destroy) {
                pellet.destroy();
            }
        });
        this.powerPellets = [];
        
        // 清理群組
        if (this.itemGroup) {
            this.itemGroup.clear(true, true);
        }
    }
    
    destroy() {
        this.clearAllItems();
        
        if (this.itemGroup) {
            this.itemGroup.destroy();
            this.itemGroup = null;
        }
    }
}
