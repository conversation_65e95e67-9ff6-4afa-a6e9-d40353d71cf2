// 地圖管理器 - 負責 Leaflet 地圖和道路網絡管理

import { gameState, mapConfigs, GAME_CONFIG } from './gameState.js';

export class MapManager {
    constructor(scene) {
        this.scene = scene;
        this.leafletMap = null;
        this.roadNetwork = [];
        this.validPositions = [];
        this.adjacencyList = new Map();
        this.isLoading = false;
    }
    
    async initializeMap(mapIndex = 0) {
        console.log('初始化地圖:', mapConfigs[mapIndex].name);

        try {
            // 清理舊地圖
            if (gameState.map) {
                gameState.map.remove();
                gameState.map = null;
            }

            // 建立新的 Leaflet 地圖
            const config = mapConfigs[mapIndex];
            gameState.currentMapIndex = mapIndex;

            // 確保地圖容器存在且可見
            const mapContainer = document.getElementById('map');
            if (!mapContainer) {
                throw new Error('找不到地圖容器');
            }

            gameState.map = L.map('map', {
                center: config.center,
                zoom: config.zoom,
                minZoom: GAME_CONFIG.MAX_MAP_ZOOM,
                maxZoom: GAME_CONFIG.MAX_MAP_ZOOM,
                zoomControl: false,
                attributionControl: false,
                scrollWheelZoom: false,
                dragging: false,
                touchZoom: false,
                doubleClickZoom: false
            });

            // 添加地圖圖層
            const tileLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                maxZoom: GAME_CONFIG.MAX_MAP_ZOOM + 1,
                attribution: '© OpenStreetMap contributors'
            });

            tileLayer.addTo(gameState.map);

            // 等待地圖載入
            await new Promise((resolve) => {
                tileLayer.on('load', resolve);
                setTimeout(resolve, 2000); // 2秒超時
            });

            gameState.map.invalidateSize();
            this.leafletMap = gameState.map;

            console.log('地圖圖層載入完成，中心點:', config.center, '縮放:', config.zoom);

            // 載入道路數據
            await this.loadRoadData(config.bounds);

            // 生成道路網絡
            await this.generateRoadNetwork(config.bounds);

            console.log('地圖初始化完成');

            return true;

        } catch (error) {
            console.error('地圖初始化失敗:', error);
            throw error;
        }
    }
    
    async loadRoadData(bounds) {
        const south = bounds.getSouth();
        const west = bounds.getWest();
        const north = bounds.getNorth();
        const east = bounds.getEast();
        
        const query = `[out:json][timeout:25];(way["highway"]["highway"!~"^(motorway|motorway_link|trunk|trunk_link|construction|proposed|razed|abandoned)$"]["area"!~"yes"]["access"!~"private"]["service"!~"^(driveway|parking_aisle|alley)$"](${south},${west},${north},${east}););out body;>;out skel qt;`;
        const url = `https://overpass-api.de/api/interpreter?data=${encodeURIComponent(query)}`;
        
        console.log('正在從 Overpass API 獲取道路數據...');
        console.log('查詢 URL:', url);

        try {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            this.roadData = data;
            console.log('道路數據載入完成，節點數:', data.elements.length);

            // 分析數據結構
            const nodes = data.elements.filter(el => el.type === 'node');
            const ways = data.elements.filter(el => el.type === 'way');
            console.log('節點數:', nodes.length, '道路數:', ways.length);

        } catch (error) {
            console.error('載入道路數據失敗:', error);
            // 如果 API 失敗，使用備用的網格生成
            console.log('使用備用網格生成方法');
            this.generateFallbackGrid(bounds);
        }
    }
    
    generateFallbackGrid(bounds) {
        console.log('生成備用網格...');

        this.validPositions = [];
        const south = bounds.getSouth();
        const west = bounds.getWest();
        const north = bounds.getNorth();
        const east = bounds.getEast();

        const stepSize = 0.0001; // 約 10 公尺間隔

        for (let lat = south; lat <= north; lat += stepSize) {
            for (let lng = west; lng <= east; lng += stepSize) {
                this.validPositions.push([lat, lng]);
            }
        }

        console.log('備用網格生成完成，位置數:', this.validPositions.length);
    }

    async generateRoadNetwork(bounds) {
        console.log('正在生成道路網絡...');

        if (!this.roadData || !this.roadData.elements) {
            console.warn('沒有道路數據，使用備用網格');
            this.generateFallbackGrid(bounds);
            return;
        }

        // 建立節點映射
        const nodeMap = new Map();
        const ways = [];

        this.roadData.elements.forEach(element => {
            if (element.type === 'node') {
                nodeMap.set(element.id, {
                    id: element.id,
                    lat: element.lat,
                    lng: element.lon
                });
            } else if (element.type === 'way') {
                ways.push(element);
            }
        });

        if (ways.length === 0) {
            console.warn('沒有找到道路，使用備用網格');
            this.generateFallbackGrid(bounds);
            return;
        }

        // 生成有效位置
        this.validPositions = [];
        const positionStep = 0.00005; // 約 5 公尺間隔

        ways.forEach(way => {
            if (!way.nodes || way.nodes.length < 2) return;

            for (let i = 0; i < way.nodes.length - 1; i++) {
                const startNode = nodeMap.get(way.nodes[i]);
                const endNode = nodeMap.get(way.nodes[i + 1]);

                if (!startNode || !endNode) continue;

                // 在兩個節點之間插入中間點
                const latDiff = endNode.lat - startNode.lat;
                const lngDiff = endNode.lng - startNode.lng;
                const distance = Math.sqrt(latDiff * latDiff + lngDiff * lngDiff);
                const steps = Math.max(1, Math.floor(distance / positionStep));

                for (let step = 0; step <= steps; step++) {
                    const ratio = step / steps;
                    const lat = startNode.lat + latDiff * ratio;
                    const lng = startNode.lng + lngDiff * ratio;

                    // 檢查是否在邊界內
                    if (bounds.contains([lat, lng])) {
                        this.validPositions.push([lat, lng]);
                    }
                }
            }
        });

        // 如果道路位置太少，使用備用網格
        if (this.validPositions.length < 50) {
            console.warn('道路位置太少，使用備用網格');
            this.generateFallbackGrid(bounds);
            return;
        }

        // 建立鄰接列表
        this.buildAdjacencyList();

        // 更新遊戲狀態
        gameState.roadNetwork = this.roadNetwork;
        gameState.validPositions = this.validPositions;
        gameState.adjacencyList = this.adjacencyList;

        console.log('道路網絡生成完成，有效位置數:', this.validPositions.length);

        // 輸出前幾個位置作為調試
        if (this.validPositions.length > 0) {
            console.log('前5個有效位置:', this.validPositions.slice(0, 5));
        }
    }
    
    buildAdjacencyList() {
        this.adjacencyList.clear();
        const connectionDistance = 0.0001; // 約 10 公尺
        
        this.validPositions.forEach((pos, index) => {
            const neighbors = [];
            
            this.validPositions.forEach((otherPos, otherIndex) => {
                if (index === otherIndex) return;
                
                const distance = this.calculateLatLngDistance(pos, otherPos);
                if (distance <= connectionDistance) {
                    neighbors.push(otherPos);
                }
            });
            
            this.adjacencyList.set(pos.toString(), neighbors);
        });
    }
    
    calculateLatLngDistance(pos1, pos2) {
        const latDiff = pos1[0] - pos2[0];
        const lngDiff = pos1[1] - pos2[1];
        return Math.sqrt(latDiff * latDiff + lngDiff * lngDiff);
    }
    
    findNearestRoadPosition(lat, lng) {
        if (this.validPositions.length === 0) {
            return null;
        }
        
        let nearestPos = this.validPositions[0];
        let minDistance = this.calculateLatLngDistance([lat, lng], nearestPos);
        
        this.validPositions.forEach(pos => {
            const distance = this.calculateLatLngDistance([lat, lng], pos);
            if (distance < minDistance) {
                minDistance = distance;
                nearestPos = pos;
            }
        });
        
        return nearestPos;
    }
    
    getRandomValidPosition() {
        if (this.validPositions.length === 0) {
            return null;
        }
        
        const randomIndex = Math.floor(Math.random() * this.validPositions.length);
        return this.validPositions[randomIndex];
    }
    
    getSpawnPoints(count) {
        const spawnPoints = [];
        const usedPositions = new Set();
        
        while (spawnPoints.length < count && spawnPoints.length < this.validPositions.length) {
            const randomPos = this.getRandomValidPosition();
            const posKey = `${randomPos[0]},${randomPos[1]}`;
            
            if (!usedPositions.has(posKey)) {
                spawnPoints.push(randomPos);
                usedPositions.add(posKey);
            }
        }
        
        return spawnPoints;
    }
    
    // 座標轉換方法
    latLngToPixel(lat, lng) {
        if (!this.leafletMap) return { x: 0, y: 0 };
        
        const point = this.leafletMap.latLngToContainerPoint([lat, lng]);
        return {
            x: point.x,
            y: point.y
        };
    }
    
    pixelToLatLng(x, y) {
        if (!this.leafletMap) return { lat: 0, lng: 0 };
        
        const latLng = this.leafletMap.containerPointToLatLng([x, y]);
        return {
            lat: latLng.lat,
            lng: latLng.lng
        };
    }
    
    // 路徑尋找 (簡化版 A*)
    findPath(startPos, endPos) {
        if (!startPos || !endPos) return [];
        
        // 簡化的路徑尋找 - 直接返回起點和終點
        // 在實際實現中，這裡應該使用 A* 算法
        return [startPos, endPos];
    }
    

    
    // 清理方法
    destroy() {
        if (gameState.map) {
            gameState.map.remove();
            gameState.map = null;
        }
        
        this.roadNetwork = [];
        this.validPositions = [];
        this.adjacencyList.clear();
    }
}
