// 鬼怪管理器

import { gameState, GAME_CONFIG, getRandomElement } from './gameState.js';

export class GhostManager {
    constructor(scene) {
        this.scene = scene;
        this.ghosts = [];
        this.ghostColors = ['red', 'pink', 'cyan', 'orange'];
        this.decisionTimer = null;
        this.decisionInterval = 500; // 500ms 決策間隔
    }
    
    spawnGhosts(spawnPoints) {
        console.log('生成鬼怪，位置數:', spawnPoints.length);
        
        // 清理現有鬼怪
        this.destroyAllGhosts();
        
        // 生成新鬼怪
        const ghostCount = Math.min(GAME_CONFIG.NUMBER_OF_GHOSTS, spawnPoints.length);
        
        for (let i = 0; i < ghostCount; i++) {
            const spawnPoint = spawnPoints[i];
            const color = this.ghostColors[i % this.ghostColors.length];
            
            const ghost = this.createGhost(spawnPoint[0], spawnPoint[1], color, i);
            this.ghosts.push(ghost);
        }
        
        // 啟動 AI 決策
        this.startAI();
        
        gameState.ghosts = this.ghosts;
        console.log('鬼怪生成完成，數量:', this.ghosts.length);
    }
    
    createGhost(lat, lng, color, id) {
        const ghost = {
            id: id,
            sprite: null,
            color: color,
            currentPosition: [lat, lng],
            targetPosition: null,
            spawnPosition: [lat, lng],
            isMoving: false,
            isScared: false,
            speed: GAME_CONFIG.GHOST_MOVE_SPEED,
            direction: 'left',
            lastDecisionTime: 0,
            moveTween: null,
            ai: {
                mode: 'scatter', // scatter, chase, scared
                target: null,
                path: [],
                lastPathUpdate: 0
            }
        };
        
        // 建立精靈
        ghost.sprite = this.scene.add.sprite(0, 0, `ghost-${color}`);
        ghost.sprite.setOrigin(0.5, 0.5);
        ghost.sprite.setDepth(5);
        
        // 設定位置
        this.updateGhostSpritePosition(ghost);
        
        // 建立簡單的移動動畫
        this.createGhostAnimation(ghost);
        
        return ghost;
    }
    
    createGhostAnimation(ghost) {
        // 建立鬼怪的浮動動畫
        this.scene.tweens.add({
            targets: ghost.sprite,
            scaleY: 0.95,
            duration: 800,
            ease: 'Sine.easeInOut',
            yoyo: true,
            repeat: -1
        });
    }
    
    updateGhostSpritePosition(ghost) {
        if (!ghost.currentPosition || !this.scene.mapManager) return;

        const mapManager = this.scene.mapManager;
        const pixelPos = mapManager.latLngToPixel(ghost.currentPosition[0], ghost.currentPosition[1]);

        ghost.sprite.setPosition(pixelPos.x, pixelPos.y);
    }
    
    startAI() {
        if (this.decisionTimer) {
            clearInterval(this.decisionTimer);
        }
        
        this.decisionTimer = setInterval(() => {
            if (!gameState.isPaused && !gameState.isGameOver) {
                this.updateGhostAI();
            }
        }, this.decisionInterval);
    }
    
    stopAI() {
        if (this.decisionTimer) {
            clearInterval(this.decisionTimer);
            this.decisionTimer = null;
        }
    }
    
    updateGhostAI() {
        this.ghosts.forEach(ghost => {
            if (!ghost.isMoving && ghost.sprite.active) {
                this.decideGhostMove(ghost);
            }
        });
    }
    
    decideGhostMove(ghost) {
        if (!this.scene.mapManager) return;

        const mapManager = this.scene.mapManager;
        
        // 根據模式決定目標
        let targetPosition = null;
        
        if (ghost.isScared) {
            // 害怕模式：遠離 Pacman
            targetPosition = this.getScaredTarget(ghost);
        } else if (ghost.ai.mode === 'chase') {
            // 追逐模式：追向 Pacman
            targetPosition = this.getChaseTarget(ghost);
        } else {
            // 散佈模式：隨機移動
            targetPosition = this.getScatterTarget(ghost);
        }
        
        if (targetPosition) {
            this.moveGhost(ghost, targetPosition);
        }
    }
    
    getChaseTarget(ghost) {
        // 追向 Pacman 的位置
        const pacman = gameState.pacman;
        if (!pacman || !pacman.getCurrentLatLngPosition()) return null;
        
        return pacman.getCurrentLatLngPosition();
    }
    
    getScaredTarget(ghost) {
        // 遠離 Pacman
        const pacman = gameState.pacman;
        if (!pacman || !pacman.getCurrentLatLngPosition()) return null;
        
        const pacmanPos = pacman.getCurrentLatLngPosition();
        const ghostPos = ghost.currentPosition;
        
        // 計算相反方向
        const latDiff = ghostPos[0] - pacmanPos[0];
        const lngDiff = ghostPos[1] - pacmanPos[1];
        
        // 放大差異以遠離
        const targetLat = ghostPos[0] + latDiff * 2;
        const targetLng = ghostPos[1] + lngDiff * 2;
        
        // 找到最近的有效位置
        const mapManager = this.scene.scene.get('GameScene').mapManager;
        return mapManager.findNearestRoadPosition(targetLat, targetLng);
    }
    
    getScatterTarget(ghost) {
        // 隨機選擇附近的位置
        const mapManager = this.scene.scene.get('GameScene').mapManager;
        const currentPos = ghost.currentPosition;
        
        // 在當前位置附近尋找目標
        const nearbyPositions = mapManager.validPositions.filter(pos => {
            const distance = mapManager.calculateLatLngDistance(currentPos, pos);
            return distance > 0.00005 && distance < 0.0002; // 5-20 公尺範圍
        });
        
        if (nearbyPositions.length === 0) {
            return mapManager.getRandomValidPosition();
        }
        
        return getRandomElement(nearbyPositions);
    }
    
    moveGhost(ghost, targetPosition) {
        if (ghost.isMoving || !targetPosition) return;
        
        ghost.isMoving = true;
        ghost.targetPosition = targetPosition;
        
        // 計算移動時間
        const mapManager = this.scene.mapManager;
        const distance = mapManager.calculateLatLngDistance(ghost.currentPosition, targetPosition);
        const pixelDistance = distance * 1000000; // 粗略轉換
        const duration = (pixelDistance / ghost.speed) * 1000;
        
        // 計算像素位置
        const startPixel = mapManager.latLngToPixel(ghost.currentPosition[0], ghost.currentPosition[1]);
        const endPixel = mapManager.latLngToPixel(targetPosition[0], targetPosition[1]);
        
        // 建立移動補間
        ghost.moveTween = this.scene.tweens.add({
            targets: ghost.sprite,
            x: endPixel.x,
            y: endPixel.y,
            duration: Math.max(200, Math.min(duration, 2000)), // 限制在 200ms-2s 之間
            ease: 'Linear',
            onComplete: () => {
                this.onGhostMoveComplete(ghost);
            }
        });
    }
    
    onGhostMoveComplete(ghost) {
        ghost.isMoving = false;
        ghost.currentPosition = ghost.targetPosition;
        ghost.targetPosition = null;
        ghost.moveTween = null;
    }
    
    scareAllGhosts() {
        this.ghosts.forEach(ghost => {
            if (ghost.sprite.active) {
                this.scareGhost(ghost);
            }
        });
    }
    
    unscareAllGhosts() {
        this.ghosts.forEach(ghost => {
            if (ghost.sprite.active) {
                this.unscareGhost(ghost);
            }
        });
    }
    
    scareGhost(ghost) {
        ghost.isScared = true;
        ghost.ai.mode = 'scared';
        
        // 更換為害怕的材質
        ghost.sprite.setTexture('ghost-scared');
        
        // 停止當前移動
        if (ghost.moveTween) {
            ghost.moveTween.stop();
            ghost.isMoving = false;
        }
        
        // 減慢速度
        ghost.speed = GAME_CONFIG.GHOST_MOVE_SPEED * 0.7;
    }
    
    unscareGhost(ghost) {
        ghost.isScared = false;
        ghost.ai.mode = Math.random() < 0.5 ? 'chase' : 'scatter';
        
        // 恢復原本的材質
        ghost.sprite.setTexture(`ghost-${ghost.color}`);
        
        // 恢復速度
        ghost.speed = GAME_CONFIG.GHOST_MOVE_SPEED;
    }
    
    respawnGhost(ghost) {
        // 停止移動
        if (ghost.moveTween) {
            ghost.moveTween.stop();
        }
        
        // 重置狀態
        ghost.isMoving = false;
        ghost.isScared = false;
        ghost.currentPosition = [...ghost.spawnPosition];
        ghost.ai.mode = 'scatter';
        
        // 恢復外觀
        ghost.sprite.setTexture(`ghost-${ghost.color}`);
        ghost.sprite.setActive(true).setVisible(true);
        ghost.sprite.setAlpha(1);
        
        // 更新位置
        this.updateGhostSpritePosition(ghost);
        
        console.log(`鬼怪 ${ghost.id} 重生`);
    }
    
    update(time, delta) {
        // 更新所有鬼怪的位置 (如果地圖有變化)
        this.updateAllPositions();
    }

    updateAllPositions() {
        this.ghosts.forEach(ghost => {
            if (ghost.sprite && ghost.sprite.active) {
                this.updateGhostSpritePosition(ghost);
            }
        });
    }
    
    destroyAllGhosts() {
        this.ghosts.forEach(ghost => {
            if (ghost.moveTween) {
                ghost.moveTween.stop();
            }
            if (ghost.sprite) {
                ghost.sprite.destroy();
            }
        });
        
        this.ghosts = [];
        this.stopAI();
    }
    
    destroy() {
        this.destroyAllGhosts();
    }
}
