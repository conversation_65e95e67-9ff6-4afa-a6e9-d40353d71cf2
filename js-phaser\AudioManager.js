// 音效管理器 - 使用 Tone.js 和 HTML5 Audio

export class AudioManager {
    constructor(scene) {
        this.scene = scene;
        this.soundsReady = false;
        this.bgmAudio = null;
        
        // Tone.js 合成器
        this.introSynth = null;
        this.dotSynth = null;
        this.powerPelletSynth = null;
        this.eatGhostSynth = null;
        this.deathSynth = null;
        
        // 音效設定
        this.masterVolume = 0.7;
        this.sfxVolume = 0.8;
        this.bgmVolume = 0.4;
        
        this.init();
    }
    
    async init() {
        console.log('初始化音效系統');
        
        try {
            // 初始化 BGM
            this.initBGM();
            
            // 初始化 Tone.js 音效
            await this.initToneJS();
            
            this.soundsReady = true;
            console.log('音效系統初始化完成');
            
        } catch (error) {
            console.warn('音效系統初始化失敗:', error);
        }
    }
    
    initBGM() {
        this.bgmAudio = document.getElementById('bgm');
        if (this.bgmAudio) {
            this.bgmAudio.volume = this.bgmVolume;
            this.bgmAudio.loop = true;
        }
    }
    
    async initToneJS() {
        if (typeof Tone === 'undefined') {
            console.warn('Tone.js 未載入，音效將不可用');
            return;
        }
        
        // 確保 Tone.js 已啟動
        if (Tone.context.state !== 'running') {
            await Tone.start();
        }
        
        // 建立各種音效合成器
        this.createSynthesizers();
    }
    
    createSynthesizers() {
        // 遊戲開始音效
        this.introSynth = new Tone.PolySynth(Tone.Synth, {
            oscillator: { type: "triangle8" },
            envelope: { attack: 0.02, decay: 0.1, sustain: 0.3, release: 0.4 },
            volume: -10
        }).toDestination();
        
        // 點數收集音效
        this.dotSynth = new Tone.MembraneSynth({
            pitchDecay: 0.008,
            octaves: 2,
            oscillator: { type: "square4" },
            envelope: { attack: 0.001, decay: 0.05, sustain: 0, release: 0.1 },
            volume: -25
        }).toDestination();
        
        // 大力丸音效
        this.powerPelletSynth = new Tone.Synth({
            oscillator: { type: "sawtooth" },
            envelope: { attack: 0.01, decay: 0.2, sustain: 0.1, release: 0.3 },
            volume: -12
        }).toDestination();
        
        // 吃鬼怪音效
        this.eatGhostSynth = new Tone.NoiseSynth({
            noise: { type: "white" },
            envelope: { attack: 0.005, decay: 0.1, sustain: 0, release: 0.1 },
            volume: -15
        }).toDestination();
        
        // 死亡音效
        this.deathSynth = new Tone.PolySynth(Tone.FMSynth, {
            harmonicity: 3.01,
            modulationIndex: 14,
            envelope: { attack: 0.2, decay: 0.3, sustain: 0.1, release: 1.2 },
            modulationEnvelope: { attack: 0.01, decay: 0.5, sustain: 0.2, release: 0.1 },
            volume: -8
        }).toDestination();
        
        console.log('音效合成器建立完成');
    }
    
    // 播放遊戲開始音效
    playStartSound() {
        if (!this.soundsReady || !this.introSynth) return;
        
        try {
            const now = Tone.now();
            this.introSynth.triggerAttackRelease(["C4", "E4", "G4"], "8n", now);
            this.introSynth.triggerAttackRelease(["E4", "G4", "C5"], "8n", now + 0.25);
            this.introSynth.triggerAttackRelease(["G4", "C5", "E5"], "4n", now + 0.5);
        } catch (error) {
            console.warn('播放開始音效失敗:', error);
        }
    }
    
    // 播放點數收集音效
    playDotSound() {
        if (!this.soundsReady || !this.dotSynth) return;
        
        try {
            this.dotSynth.triggerAttackRelease("C4", "32n", Tone.now());
        } catch (error) {
            console.warn('播放點數音效失敗:', error);
        }
    }
    
    // 播放大力丸音效
    playPowerPelletSound() {
        if (!this.soundsReady || !this.powerPelletSynth) return;
        
        try {
            const now = Tone.now();
            this.powerPelletSynth.triggerAttackRelease("A4", "16n", now);
            this.powerPelletSynth.triggerAttackRelease("C#5", "16n", now + 0.1);
            this.powerPelletSynth.triggerAttackRelease("E5", "8n", now + 0.2);
        } catch (error) {
            console.warn('播放大力丸音效失敗:', error);
        }
    }
    
    // 播放吃鬼怪音效
    playEatGhostSound() {
        if (!this.soundsReady || !this.eatGhostSynth) return;
        
        try {
            this.eatGhostSynth.triggerAttackRelease("0.2n", Tone.now());
        } catch (error) {
            console.warn('播放吃鬼怪音效失敗:', error);
        }
    }
    
    // 播放死亡音效
    playDeathSound() {
        if (!this.soundsReady || !this.deathSynth) return;
        
        try {
            const now = Tone.now();
            this.deathSynth.triggerAttackRelease(["C3", "Eb3", "Gb3"], "1n", now);
            this.deathSynth.triggerAttackRelease(["C2", "Eb2", "Gb2"], "1n", now + 0.1);
        } catch (error) {
            console.warn('播放死亡音效失敗:', error);
        }
    }
    
    // 播放背景音樂
    playBGM() {
        if (!this.bgmAudio) return;
        
        try {
            if (this.bgmAudio.paused) {
                this.bgmAudio.play().catch(error => {
                    console.warn("BGM 自動播放被瀏覽器阻止:", error);
                });
            }
        } catch (error) {
            console.warn('播放 BGM 失敗:', error);
        }
    }
    
    // 停止背景音樂
    stopBGM() {
        if (!this.bgmAudio) return;
        
        try {
            this.bgmAudio.pause();
            this.bgmAudio.currentTime = 0;
        } catch (error) {
            console.warn('停止 BGM 失敗:', error);
        }
    }
    
    // 暫停背景音樂
    pauseBGM() {
        if (!this.bgmAudio) return;
        
        try {
            this.bgmAudio.pause();
        } catch (error) {
            console.warn('暫停 BGM 失敗:', error);
        }
    }
    
    // 恢復背景音樂
    resumeBGM() {
        if (!this.bgmAudio) return;
        
        try {
            this.bgmAudio.play().catch(error => {
                console.warn('恢復 BGM 失敗:', error);
            });
        } catch (error) {
            console.warn('恢復 BGM 失敗:', error);
        }
    }
    
    // 設定主音量
    setMasterVolume(volume) {
        this.masterVolume = Math.max(0, Math.min(1, volume));
        this.updateVolumes();
    }
    
    // 設定音效音量
    setSFXVolume(volume) {
        this.sfxVolume = Math.max(0, Math.min(1, volume));
        this.updateVolumes();
    }
    
    // 設定背景音樂音量
    setBGMVolume(volume) {
        this.bgmVolume = Math.max(0, Math.min(1, volume));
        if (this.bgmAudio) {
            this.bgmAudio.volume = this.bgmVolume * this.masterVolume;
        }
    }
    
    // 更新所有音量
    updateVolumes() {
        const finalSFXVolume = this.sfxVolume * this.masterVolume;
        const finalBGMVolume = this.bgmVolume * this.masterVolume;
        
        // 更新 Tone.js 音效音量
        if (this.introSynth) this.introSynth.volume.value = -10 + (finalSFXVolume * 10);
        if (this.dotSynth) this.dotSynth.volume.value = -25 + (finalSFXVolume * 15);
        if (this.powerPelletSynth) this.powerPelletSynth.volume.value = -12 + (finalSFXVolume * 8);
        if (this.eatGhostSynth) this.eatGhostSynth.volume.value = -15 + (finalSFXVolume * 10);
        if (this.deathSynth) this.deathSynth.volume.value = -8 + (finalSFXVolume * 5);
        
        // 更新 BGM 音量
        if (this.bgmAudio) {
            this.bgmAudio.volume = finalBGMVolume;
        }
    }
    
    // 靜音/取消靜音
    toggleMute() {
        if (this.masterVolume > 0) {
            this.previousVolume = this.masterVolume;
            this.setMasterVolume(0);
        } else {
            this.setMasterVolume(this.previousVolume || 0.7);
        }
    }
    
    // 清理資源
    destroy() {
        // 停止 BGM
        this.stopBGM();
        
        // 清理 Tone.js 合成器
        if (this.introSynth) {
            this.introSynth.dispose();
            this.introSynth = null;
        }
        if (this.dotSynth) {
            this.dotSynth.dispose();
            this.dotSynth = null;
        }
        if (this.powerPelletSynth) {
            this.powerPelletSynth.dispose();
            this.powerPelletSynth = null;
        }
        if (this.eatGhostSynth) {
            this.eatGhostSynth.dispose();
            this.eatGhostSynth = null;
        }
        if (this.deathSynth) {
            this.deathSynth.dispose();
            this.deathSynth = null;
        }
        
        this.soundsReady = false;
        console.log('音效系統已清理');
    }
}
