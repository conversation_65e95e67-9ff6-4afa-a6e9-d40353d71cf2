// Phaser.js 版本的遊戲狀態管理

// 遊戲常數
export const GAME_CONFIG = {
    PACMAN_BASE_SPEED: 120, // Phaser 中的像素/秒
    GHOST_MOVE_SPEED: 100,
    MAX_MAP_ZOOM: 18,
    MAX_DELTA_TIME: 100,
    NUMBER_OF_GHOSTS: 7,
    GAME_WIDTH: 800,
    GAME_HEIGHT: 600,
    COLLISION_DISTANCE: 15, // Phaser 中的像素距離
    DOT_POINTS: 20,
    POWER_PELLET_POINTS: 50,
    GHOST_POINTS: 150
};

// 地圖設定 (與原版相同)
export const mapConfigs = [
    { 
        name: "台北市中心", 
        center: [25.0330, 121.5654], 
        zoom: GAME_CONFIG.MAX_MAP_ZOOM, 
        bounds: L.latLngBounds(
            [25.0290, 121.5604], // 西南角
            [25.0370, 121.5704]  // 東北角
        )
    },
    { 
        name: "台中市區", 
        center: [24.1477, 120.6736], 
        zoom: GAME_CONFIG.MAX_MAP_ZOOM, 
        bounds: L.latLngBounds(
            [24.1437, 120.6686],
            [24.1517, 120.6786]
        )
    },
    { 
        name: "高雄市區", 
        center: [22.6273, 120.3014], 
        zoom: GAME_CONFIG.MAX_MAP_ZOOM, 
        bounds: L.latLngBounds(
            [22.6233, 120.2964],
            [22.6313, 120.3064]
        )
    }
];

// 主要遊戲狀態
export let gameState = {
    // Phaser 相關
    phaserGame: null,
    gameScene: null,
    
    // Leaflet 地圖相關
    map: null,
    currentMapIndex: 0,
    
    // 遊戲物件 (Phaser 管理)
    pacman: null,
    ghosts: [],
    dots: [],
    powerPellets: [],
    
    // 遊戲數據
    score: 0,
    level: 1,
    gameTime: 600,
    gameStartTime: 0,
    
    // 生命系統
    healthSystem: {
        lives: 3,
        maxLives: 3,
        currentHealth: 100,
        maxHealth: 100
    },
    
    // 遊戲狀態
    isPaused: false,
    isGameOver: false,
    isLosingLife: false,
    isRoundTransitioning: false,
    canMove: false,
    powerMode: false,
    powerModeTimer: null,
    
    // 地圖數據
    roadNetwork: [],
    validPositions: [],
    adjacencyList: new Map(),
    ghostSpawnPoints: [],
    
    // 計數器
    dotsCollected: 0,
    totalDots: 0,
    
    // 開發者功能
    gameSpeedMultiplier: 1,
    pacmanSpeedMultiplier: 1.0,
    godMode: false,
    autoPilotMode: false,
    
    // 座標轉換快取
    coordinateCache: new Map(),
    
    // 效能監控
    fpsFrameTimes: [],
    lastFpsUpdate: 0
};

// 座標轉換工具類
export class CoordinateConverter {
    constructor(leafletMap, phaserGame) {
        this.leafletMap = leafletMap;
        this.phaserGame = phaserGame;
        this.cache = new Map();
    }
    
    // 將 Leaflet 經緯度轉換為 Phaser 像素座標
    latLngToPhaser(lat, lng) {
        // 不使用快取，每次都重新計算以確保準確性
        const point = this.leafletMap.latLngToContainerPoint([lat, lng]);
        return {
            x: point.x,
            y: point.y
        };
    }
    
    // 將 Phaser 像素座標轉換為 Leaflet 經緯度
    phaserToLatLng(x, y) {
        const latLng = this.leafletMap.containerPointToLatLng([x, y]);
        return {
            lat: latLng.lat,
            lng: latLng.lng
        };
    }
    
    // 清除快取
    clearCache() {
        this.cache.clear();
    }
}

// 遊戲事件系統
export class GameEventEmitter extends Phaser.Events.EventEmitter {
    constructor() {
        super();
    }
    
    // 發送遊戲事件
    emitGameEvent(eventName, data) {
        this.emit(eventName, data);
    }
    
    // 監聽遊戲事件
    onGameEvent(eventName, callback, context) {
        this.on(eventName, callback, context);
    }
}

// 全域事件發射器
export const gameEvents = new GameEventEmitter();

// 遊戲狀態重置函數
export function resetGameState() {
    gameState.score = 0;
    gameState.level = 1;
    gameState.gameTime = 600;
    gameState.isPaused = false;
    gameState.isGameOver = false;
    gameState.isLosingLife = false;
    gameState.isRoundTransitioning = false;
    gameState.powerMode = false;
    gameState.dotsCollected = 0;
    gameState.canMove = false;
    
    // 重置生命系統
    gameState.healthSystem.lives = 3;
    gameState.healthSystem.currentHealth = 100;
    
    // 清空遊戲物件陣列
    gameState.ghosts = [];
    gameState.dots = [];
    gameState.powerPellets = [];
    gameState.ghostSpawnPoints = [];
    
    // 清空地圖數據
    gameState.roadNetwork = [];
    gameState.validPositions = [];
    gameState.adjacencyList.clear();
    
    // 清除計時器
    if (gameState.powerModeTimer) {
        clearTimeout(gameState.powerModeTimer);
        gameState.powerModeTimer = null;
    }
    
    // 清除座標快取
    if (gameState.coordinateCache) {
        gameState.coordinateCache.clear();
    }
}

// 工具函數
export function getRandomElement(array) {
    return array[Math.floor(Math.random() * array.length)];
}

export function calculateDistance(pos1, pos2) {
    const dx = pos1.x - pos2.x;
    const dy = pos1.y - pos2.y;
    return Math.sqrt(dx * dx + dy * dy);
}

export function normalizeAngle(angle) {
    while (angle < 0) angle += 2 * Math.PI;
    while (angle >= 2 * Math.PI) angle -= 2 * Math.PI;
    return angle;
}

// 排行榜資料
export let leaderboard = [];

// 載入排行榜
export function loadLeaderboard() {
    try {
        const saved = localStorage.getItem('pacmap-leaderboard');
        if (saved) {
            leaderboard = JSON.parse(saved);
        }
    } catch (error) {
        console.warn('無法載入排行榜:', error);
        leaderboard = [];
    }
}

// 儲存排行榜
export function saveLeaderboard() {
    try {
        localStorage.setItem('pacmap-leaderboard', JSON.stringify(leaderboard));
    } catch (error) {
        console.warn('無法儲存排行榜:', error);
    }
}

// 新增分數到排行榜
export function addToLeaderboard(score) {
    leaderboard.push(score);
    leaderboard.sort((a, b) => b - a);
    leaderboard = leaderboard.slice(0, 10); // 只保留前 10 名
    saveLeaderboard();
}

// 初始化
loadLeaderboard();
