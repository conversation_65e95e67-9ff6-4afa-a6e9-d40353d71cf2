<!DOCTYPE html>
<html lang="zh-TW">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PAC-MAP - Phaser.js 版本</title>

    <!-- Leaflet CSS -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.css"
    />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Cubic+11&display=swap"
      rel="stylesheet"
    />

    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Cubic 11", cursive;
        background: #000;
        overflow: hidden;
        color: #fff;
      }

      /* 遊戲容器 - 混合架構 */
      .game-container {
        position: relative;
        width: 100vw;
        height: 100vh;
        background-color: #000;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      /* 遊戲視窗 */
      #game-viewport {
        position: relative;
        width: 100%;
        height: 100%;
        max-width: 100vw;
        max-height: 100vh;
        aspect-ratio: 16 / 9;
      }

      /* Leaflet 地圖層 - 背景層 */
      #map {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        background-color: #f0f0f0; /* 添加背景色以便調試 */
      }

      /* Phaser 遊戲層 - 前景層 */
      #phaser-game {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 2;
        pointer-events: none; /* 讓地圖事件穿透 */
      }

      /* 確保 Phaser canvas 透明 */
      #phaser-game canvas {
        background: transparent !important;
      }

      /* UI 層 - 最上層 */
      .ui-layer {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 3;
        pointer-events: none;
      }

      /* 允許特定 UI 元素接收事件 */
      .ui-interactive {
        pointer-events: auto;
      }

      /* FPS 顯示 */
      .fps-overlay {
        position: absolute;
        top: 10px;
        right: 12px;
        z-index: 1000;
        padding: 6px 8px;
        background: rgba(0, 0, 0, 0.65);
        border: 1px solid #555;
        color: #0f0;
        font-family: "Cubic 11", cursive;
        font-size: 12px;
        user-select: none;
      }

      /* 遊戲 UI */
      .game-ui {
        position: absolute;
        top: 20px;
        left: 20px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 15px;
        border-radius: 10px;
        font-family: "Cubic 11", cursive;
        font-size: 14px;
        border: 1px solid #555;
        pointer-events: auto;
      }

      .ui-row {
        display: flex;
        justify-content: space-between;
        margin: 5px 0;
        min-width: 300px;
      }

      /* 血條容器 */
      .health-bar-container {
        width: 100%;
        height: 14px;
        background-color: #333;
        border: 2px solid #555;
        border-radius: 7px;
        padding: 2px;
        box-sizing: border-box;
        margin: 8px 0;
      }

      .health-bar {
        height: 100%;
        width: 100%;
        background-color: #00ff00;
        border-radius: 4px;
        transition: width 0.5s ease-in-out, background-color 0.5s ease-in-out;
      }

      /* 螢幕覆蓋層 */
      .screen-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 20px;
        text-align: center;
        overflow-y: auto;
        box-sizing: border-box;
        z-index: 1000;
        pointer-events: auto;
      }

      .start-screen {
        background: rgba(0, 0, 0, 0.3);
      }

      .game-title {
        font-size: clamp(2.5rem, 8vw, 4.5rem);
        color: #ffff00;
        text-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px #ffff00,
          0 0 20px #ffff00, 0 0 25px #ffff00, 0 0 30px #ff0000, 0 0 35px #ff0000;
        margin-bottom: 1.5rem;
        animation: gameTitlePulse 2s infinite ease-in-out;
        letter-spacing: 0.1em;
      }

      @keyframes gameTitlePulse {
        0%,
        100% {
          transform: scale(1);
          opacity: 1;
        }
        50% {
          transform: scale(1.05);
          opacity: 0.85;
        }
      }

      /* 按鈕樣式 */
      .pacman-pixel-button {
        font-family: "Cubic 11", cursive;
        background-color: #2121de;
        color: #ffff00;
        border: 3px solid #000000;
        padding: 12px 24px;
        font-size: clamp(0.9rem, 3vw, 1.2rem);
        text-transform: uppercase;
        border-radius: 0;
        box-shadow: 4px 4px 0px #000000c0;
        cursor: pointer;
        transition: transform 0.05s ease-out, box-shadow 0.05s ease-out,
          background-color 0.05s ease-out;
        margin: 10px;
        display: inline-block;
        outline: none;
        min-width: 180px;
      }

      .pacman-pixel-button:hover {
        background-color: #4242ff;
        color: #ffff66;
        box-shadow: 2px 2px 0px #000000c0;
        transform: translate(2px, 2px);
      }

      .pacman-pixel-button:active {
        background-color: #0000b3;
        box-shadow: 0px 0px 0px #000000c0;
        transform: translate(4px, 4px);
      }

      /* 載入畫面 */
      .loading-screen {
        background: rgba(0, 0, 0, 0.9);
        display: none;
      }

      .loading-text {
        font-size: 1.5rem;
        color: #ffff00;
        margin-bottom: 20px;
      }

      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid #333;
        border-top: 3px solid #ffff00;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      /* 隱藏元素 */
      .hidden {
        display: none !important;
      }
    </style>
  </head>
  <body>
    <div class="game-container">
      <!-- 地圖層 (背景) -->
      <div id="game-viewport">
        <div id="map"></div>

        <!-- Phaser 遊戲層 (前景) -->
        <div id="phaser-game"></div>
      </div>

      <!-- UI 層 -->
      <div class="ui-layer">
        <!-- FPS 顯示 -->
        <div class="fps-overlay ui-interactive" id="fpsDisplay">FPS: --</div>

        <!-- 遊戲 UI -->
        <div class="game-ui ui-interactive hidden" id="gameUI">
          <div class="ui-row">
            <span>分數: <span id="score">0</span></span>
            <span>生命: <span id="lives">3</span></span>
          </div>
          <div class="health-bar-container">
            <div id="healthBar" class="health-bar"></div>
          </div>
          <div class="ui-row">
            <span>關卡: <span id="level">1</span></span>
            <span>時間: <span id="timer">5:00</span></span>
          </div>
          <div class="ui-row">
            <span>剩餘點數: <span id="dotsLeft">0</span></span>
            <span>最高分: <span id="highScore">0</span></span>
          </div>
        </div>

        <!-- 開始畫面 -->
        <div
          class="screen-overlay start-screen ui-interactive"
          id="startScreen"
        >
          <h1 class="game-title">PAC-MAP</h1>
          <div>
            <button class="pacman-pixel-button" id="startGameBtn">
              開始遊戲
            </button>
            <button class="pacman-pixel-button" id="instructionsBtn">
              遊戲說明
            </button>
          </div>
          <p style="margin-top: 20px; color: #ccc">Phaser.js 版本</p>
        </div>

        <!-- 載入畫面 -->
        <div
          class="screen-overlay loading-screen ui-interactive"
          id="loadingScreen"
        >
          <div class="loading-text" id="loadingText">正在載入...</div>
          <div class="loading-spinner"></div>
        </div>
      </div>
    </div>

    <!-- 音效 -->
    <audio id="bgm" loop>
      <source src="audio/Pacmap_bgm.wav" type="audio/wav" />
    </audio>

    <!-- 外部庫 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/phaser/3.70.0/phaser.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tone/14.8.49/Tone.min.js"></script>

    <!-- 遊戲腳本 -->
    <script type="module" src="js-phaser/main.js"></script>
  </body>
</html>
