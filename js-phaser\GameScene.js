// Phaser.js 主要遊戲場景

import { gameState, GAME_CONFIG, gameEvents, CoordinateConverter } from './gameState.js';
import { MapManager } from './MapManager.js';
import { PacmanPlayer } from './PacmanPlayer.js';
import { GhostManager } from './GhostManager.js';
import { ItemManager } from './ItemManager.js';
import { AudioManager } from './AudioManager.js';

export class GameScene extends Phaser.Scene {
    constructor() {
        super({ key: 'GameScene' });
        
        // 管理器
        this.mapManager = null;
        this.pacmanPlayer = null;
        this.ghostManager = null;
        this.itemManager = null;
        this.audioManager = null;
        this.coordinateConverter = null;
        
        // 輸入控制
        this.cursors = null;
        this.wasdKeys = null;
        
        // 遊戲狀態
        this.gameTimer = null;
        this.lastUpdateTime = 0;
        
        // 效能監控
        this.fpsText = null;
        this.lastFpsTime = 0;
        this.frameCount = 0;
    }
    
    preload() {
        // 建立簡單的幾何圖形作為遊戲物件
        this.createGameAssets();
    }
    
    create() {
        console.log('Phaser GameScene 已建立');

        // 設定遊戲場景引用
        gameState.gameScene = this;

        // 初始化音效管理器
        this.audioManager = new AudioManager(this);

        // 設定輸入控制
        this.setupInput();

        // 設定事件監聽
        this.setupEventListeners();

        // 初始化 UI 更新
        this.setupUIUpdates();

        console.log('遊戲場景初始化完成');

        // 發送場景準備就緒事件
        this.events.emit('create');
    }
    
    createGameAssets() {
        console.log('建立遊戲資源');

        // 建立 Pacman 圖形
        const pacmanGraphics = this.add.graphics();
        pacmanGraphics.fillStyle(0xFFFF00);
        pacmanGraphics.fillCircle(12, 12, 12);
        pacmanGraphics.generateTexture('pacman', 24, 24);
        pacmanGraphics.destroy();

        // 建立 Ghost 圖形 - 簡單圓形設計 (類似原版)
        const ghostRedGraphics = this.add.graphics();
        ghostRedGraphics.fillStyle(0xFF0000);
        ghostRedGraphics.fillCircle(10, 10, 8);
        ghostRedGraphics.generateTexture('ghost-red', 20, 20);
        ghostRedGraphics.destroy();

        const ghostPinkGraphics = this.add.graphics();
        ghostPinkGraphics.fillStyle(0xFFC0CB);
        ghostPinkGraphics.fillCircle(10, 10, 8);
        ghostPinkGraphics.generateTexture('ghost-pink', 20, 20);
        ghostPinkGraphics.destroy();

        const ghostCyanGraphics = this.add.graphics();
        ghostCyanGraphics.fillStyle(0x00FFFF);
        ghostCyanGraphics.fillCircle(10, 10, 8);
        ghostCyanGraphics.generateTexture('ghost-cyan', 20, 20);
        ghostCyanGraphics.destroy();

        const ghostOrangeGraphics = this.add.graphics();
        ghostOrangeGraphics.fillStyle(0xFFB84D);
        ghostOrangeGraphics.fillCircle(10, 10, 8);
        ghostOrangeGraphics.generateTexture('ghost-orange', 20, 20);
        ghostOrangeGraphics.destroy();

        // 建立害怕的鬼怪
        const ghostScaredGraphics = this.add.graphics();
        ghostScaredGraphics.fillStyle(0x2222DD);
        ghostScaredGraphics.fillCircle(10, 10, 8);
        ghostScaredGraphics.generateTexture('ghost-scared', 20, 20);
        ghostScaredGraphics.destroy();

        // 建立點數 - 放大讓它更明顯
        const dotGraphics = this.add.graphics();
        dotGraphics.fillStyle(0xFFFF00);
        dotGraphics.fillCircle(4, 4, 4); // 放大點數
        dotGraphics.generateTexture('dot', 8, 8);
        dotGraphics.destroy();

        // 建立大力丸
        const pelletGraphics = this.add.graphics();
        pelletGraphics.fillStyle(0xFFFF00);
        pelletGraphics.fillCircle(6, 6, 6);
        pelletGraphics.generateTexture('power-pellet', 12, 12);
        pelletGraphics.destroy();

        console.log('遊戲資源建立完成');
    }
    
    setupInput() {
        // 建立方向鍵
        this.cursors = this.input.keyboard.createCursorKeys();
        
        // 建立 WASD 鍵
        this.wasdKeys = this.input.keyboard.addKeys('W,S,A,D');
        
        // 建立空白鍵 (暫停)
        this.spaceKey = this.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.SPACE);
        
        // 監聽按鍵事件
        this.input.keyboard.on('keydown', this.handleKeyDown, this);
    }
    
    handleKeyDown(event) {
        console.log('按鍵事件:', event.code, 'canMove:', gameState.canMove, 'isGameOver:', gameState.isGameOver);

        if (gameState.isGameOver || !gameState.canMove) return;

        // 暫停/繼續
        if (event.code === 'Space') {
            event.preventDefault();
            this.togglePause();
            return;
        }

        if (gameState.isPaused) return;

        // 移動控制
        let direction = null;
        switch (event.code) {
            case 'KeyW':
            case 'ArrowUp':
                direction = 'up';
                break;
            case 'KeyS':
            case 'ArrowDown':
                direction = 'down';
                break;
            case 'KeyA':
            case 'ArrowLeft':
                direction = 'left';
                break;
            case 'KeyD':
            case 'ArrowRight':
                direction = 'right';
                break;
        }

        if (direction && this.pacmanPlayer) {
            console.log('按鍵移動:', direction, 'pacmanPlayer存在:', !!this.pacmanPlayer);
            this.pacmanPlayer.setDirection(direction);
        }

        // 停止移動 (ESC 鍵)
        if (event.code === 'Escape' && this.pacmanPlayer) {
            this.pacmanPlayer.stopAutoMovement();
        }
    }
    
    setupEventListeners() {
        // 監聽遊戲事件
        gameEvents.on('pacman-eat-dot', this.onPacmanEatDot, this);
        gameEvents.on('pacman-eat-power-pellet', this.onPacmanEatPowerPellet, this);
        gameEvents.on('pacman-eat-ghost', this.onPacmanEatGhost, this);
        gameEvents.on('pacman-hit-ghost', this.onPacmanHitGhost, this);
        gameEvents.on('level-complete', this.onLevelComplete, this);
        gameEvents.on('game-over', this.onGameOver, this);
    }
    
    setupUIUpdates() {
        // 每秒更新一次遊戲時間
        this.gameTimer = this.time.addEvent({
            delay: 1000,
            callback: this.updateGameTime,
            callbackScope: this,
            loop: true
        });
    }
    
    update(time, delta) {
        if (gameState.isPaused || gameState.isGameOver) return;

        // 更新效能監控
        this.updateFPS(time);

        // 更新所有物件的位置 (重要：確保座標同步)
        this.updateAllPositions();

        // 更新遊戲物件
        if (this.pacmanPlayer) {
            this.pacmanPlayer.update(time, delta);
        }

        if (this.ghostManager) {
            this.ghostManager.update(time, delta);
        }

        if (this.itemManager) {
            this.itemManager.update(time, delta);
        }

        // 檢查碰撞
        this.checkCollisions();

        // 更新 UI
        this.updateUI();
    }

    updateAllPositions() {
        // 只在必要時更新位置，避免不必要的重新計算
        // 暫時註解掉，看看是否解決點數跟著移動的問題
        /*
        if (this.pacmanPlayer) {
            this.pacmanPlayer.updateSpritePosition();
        }

        if (this.ghostManager) {
            this.ghostManager.updateAllPositions();
        }

        if (this.itemManager) {
            this.itemManager.updateItemPositions();
        }
        */
    }
    
    checkCollisions() {
        if (!this.pacmanPlayer || !this.pacmanPlayer.sprite || !this.itemManager) return;

        const pacmanPos = this.pacmanPlayer.sprite;

        // 檢查與點數的碰撞
        this.itemManager.dots.forEach(dot => {
            if (dot.active && Phaser.Geom.Intersects.CircleToCircle(
                new Phaser.Geom.Circle(pacmanPos.x, pacmanPos.y, 12),
                new Phaser.Geom.Circle(dot.x, dot.y, 8)
            )) {
                console.log('Pacman 收集到點數!');
                this.collectDot(dot);
            }
        });
        
        // 檢查與大力丸的碰撞
        this.itemManager.powerPellets.forEach(pellet => {
            if (pellet.active && Phaser.Geom.Intersects.CircleToCircle(
                new Phaser.Geom.Circle(pacmanPos.x, pacmanPos.y, 12),
                new Phaser.Geom.Circle(pellet.x, pellet.y, 8)
            )) {
                this.collectPowerPellet(pellet);
            }
        });
        
        // 檢查與鬼怪的碰撞
        this.ghostManager.ghosts.forEach(ghost => {
            if (ghost.sprite && ghost.sprite.active && Phaser.Geom.Intersects.CircleToCircle(
                new Phaser.Geom.Circle(pacmanPos.x, pacmanPos.y, 12),
                new Phaser.Geom.Circle(ghost.sprite.x, ghost.sprite.y, 10)
            )) {
                if (gameState.powerMode && ghost.isScared) {
                    this.eatGhost(ghost);
                } else if (!ghost.isScared) {
                    this.hitByGhost();
                }
            }
        });
    }
    
    collectDot(dot) {
        dot.setActive(false).setVisible(false);
        gameState.score += GAME_CONFIG.DOT_POINTS;
        gameState.dotsCollected++;
        
        this.audioManager.playDotSound();
        gameEvents.emit('pacman-eat-dot', { dot, score: GAME_CONFIG.DOT_POINTS });
        
        this.checkLevelComplete();
    }
    
    collectPowerPellet(pellet) {
        pellet.setActive(false).setVisible(false);
        gameState.score += GAME_CONFIG.POWER_PELLET_POINTS;
        
        this.activatePowerMode();
        this.audioManager.playPowerPelletSound();
        gameEvents.emit('pacman-eat-power-pellet', { pellet, score: GAME_CONFIG.POWER_PELLET_POINTS });
        
        this.checkLevelComplete();
    }
    
    eatGhost(ghost) {
        ghost.sprite.setActive(false).setVisible(false);
        gameState.score += GAME_CONFIG.GHOST_POINTS;
        
        this.audioManager.playEatGhostSound();
        gameEvents.emit('pacman-eat-ghost', { ghost, score: GAME_CONFIG.GHOST_POINTS });
        
        // 重生鬼怪
        this.time.delayedCall(3000, () => {
            this.ghostManager.respawnGhost(ghost);
        });
    }
    
    hitByGhost() {
        if (gameState.godMode || gameState.isLosingLife) return;
        
        gameState.isLosingLife = true;
        gameState.healthSystem.lives--;
        
        this.audioManager.playDeathSound();
        gameEvents.emit('pacman-hit-ghost');
        
        if (gameState.healthSystem.lives <= 0) {
            this.endGame();
        } else {
            this.respawnPacman();
        }
    }
    
    activatePowerMode() {
        gameState.powerMode = true;
        this.ghostManager.scareAllGhosts();
        
        // 清除之前的計時器
        if (gameState.powerModeTimer) {
            clearTimeout(gameState.powerModeTimer);
        }
        
        // 設定新的計時器
        gameState.powerModeTimer = setTimeout(() => {
            gameState.powerMode = false;
            this.ghostManager.unscareAllGhosts();
        }, 10000); // 10 秒
    }
    
    checkLevelComplete() {
        const activeDots = this.itemManager.dots.filter(dot => dot.active).length;
        const activePellets = this.itemManager.powerPellets.filter(pellet => pellet.active).length;
        
        if (activeDots === 0 && activePellets === 0) {
            gameEvents.emit('level-complete');
            this.nextLevel();
        }
    }
    
    nextLevel() {
        gameState.level++;
        gameState.isRoundTransitioning = true;
        
        // 重新生成物品
        this.time.delayedCall(2000, () => {
            this.itemManager.generateItems();
            gameState.isRoundTransitioning = false;
        });
    }
    
    respawnPacman() {
        this.time.delayedCall(2000, () => {
            this.pacmanPlayer.respawn();
            gameState.isLosingLife = false;
        });
    }
    
    endGame() {
        gameState.isGameOver = true;
        gameState.canMove = false;
        
        if (this.gameTimer) {
            this.gameTimer.destroy();
        }
        
        gameEvents.emit('game-over', { score: gameState.score });
    }
    
    togglePause() {
        gameState.isPaused = !gameState.isPaused;
        
        if (gameState.isPaused) {
            this.scene.pause();
        } else {
            this.scene.resume();
        }
    }
    
    updateGameTime() {
        if (!gameState.isPaused && !gameState.isGameOver) {
            gameState.gameTime--;
            if (gameState.gameTime <= 0) {
                this.endGame();
            }
        }
    }
    
    updateFPS(time) {
        this.frameCount++;
        if (time - this.lastFpsTime >= 1000) {
            const fps = Math.round(this.frameCount * 1000 / (time - this.lastFpsTime));
            document.getElementById('fpsDisplay').textContent = `FPS: ${fps}`;
            this.frameCount = 0;
            this.lastFpsTime = time;
        }
    }
    
    updateUI() {
        // 更新分數和其他 UI 元素
        document.getElementById('score').textContent = gameState.score;
        document.getElementById('lives').textContent = gameState.healthSystem.lives;
        document.getElementById('level').textContent = gameState.level;
        
        const minutes = Math.floor(gameState.gameTime / 60);
        const seconds = gameState.gameTime % 60;
        document.getElementById('timer').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        
        const activeDots = this.itemManager ? this.itemManager.dots.filter(dot => dot.active).length : 0;
        const activePellets = this.itemManager ? this.itemManager.powerPellets.filter(pellet => pellet.active).length : 0;
        document.getElementById('dotsLeft').textContent = activeDots + activePellets;
        
        // 更新血條
        const healthPercentage = (gameState.healthSystem.currentHealth / gameState.healthSystem.maxHealth) * 100;
        const healthBar = document.getElementById('healthBar');
        if (healthBar) {
            healthBar.style.width = `${healthPercentage}%`;
            
            // 根據血量改變顏色
            if (healthPercentage > 60) {
                healthBar.style.backgroundColor = '#00ff00';
            } else if (healthPercentage > 30) {
                healthBar.style.backgroundColor = '#ffff00';
            } else {
                healthBar.style.backgroundColor = '#ff0000';
            }
        }
    }
    
    // 事件處理器
    onPacmanEatDot(data) {
        console.log('Pacman 吃到點數:', data.score);
    }
    
    onPacmanEatPowerPellet(data) {
        console.log('Pacman 吃到大力丸:', data.score);
    }
    
    onPacmanEatGhost(data) {
        console.log('Pacman 吃到鬼怪:', data.score);
    }
    
    onPacmanHitGhost() {
        console.log('Pacman 被鬼怪撞到');
    }
    
    onLevelComplete() {
        console.log('關卡完成');
    }
    
    onGameOver(data) {
        console.log('遊戲結束，分數:', data.score);
    }

    // 初始化遊戲元素的方法
    initializeGameElements(mapManager) {
        console.log('初始化遊戲元素');

        // 設定地圖管理器引用
        this.mapManager = mapManager;

        // 初始化座標轉換器
        this.coordinateConverter = new CoordinateConverter(gameState.map, this.game);

        // 初始化遊戲物件管理器
        this.pacmanPlayer = new PacmanPlayer(this);
        this.ghostManager = new GhostManager(this);
        this.itemManager = new ItemManager(this);

        console.log('遊戲元素初始化完成');
    }
}
