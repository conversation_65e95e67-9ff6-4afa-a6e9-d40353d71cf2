// Pacman 玩家類別

import { gameState, GAME_CONFIG } from './gameState.js';

export class PacmanPlayer {
    constructor(scene) {
        this.scene = scene;
        this.sprite = null;
        this.currentPosition = null; // 經緯度位置
        this.targetPosition = null;
        this.isMoving = false;
        this.currentDirection = 'left';
        this.nextDirection = null;
        this.speed = GAME_CONFIG.PACMAN_BASE_SPEED;
        this.autoMoving = false; // 是否正在自動移動
        this.spawnPosition = null;
        
        // 動畫相關
        this.mouthAnimation = null;
        this.rotationTween = null;
        
        this.createPacman();
    }
    
    createPacman() {
        // 建立 Pacman 精靈
        this.sprite = this.scene.add.sprite(0, 0, 'pacman');
        this.sprite.setOrigin(0.5, 0.5);
        this.sprite.setDepth(10); // 確保在其他物件之上
        
        // 建立嘴巴動畫
        this.createMouthAnimation();
        
        // 設定初始位置 (暫時放在螢幕中央)
        this.sprite.setPosition(
            this.scene.cameras.main.width / 2,
            this.scene.cameras.main.height / 2
        );
        
        // 隱藏直到正確定位
        this.sprite.setVisible(false);
        
        gameState.pacman = this;
    }
    
    createMouthAnimation() {
        // 建立 Pacman 嘴巴開合動畫的圖形
        const graphics = this.scene.add.graphics();
        
        // 完整圓形 (嘴巴閉合)
        graphics.fillStyle(0xFFFF00);
        graphics.fillCircle(12, 12, 12);
        graphics.generateTexture('pacman-closed', 24, 24);
        graphics.clear();
        
        // 嘴巴張開 (缺一塊)
        graphics.fillStyle(0xFFFF00);
        graphics.slice(12, 12, 12, Phaser.Math.DegToRad(-30), Phaser.Math.DegToRad(30), true);
        graphics.fillPath();
        graphics.generateTexture('pacman-open', 24, 24);
        graphics.destroy();
        
        // 建立動畫
        this.scene.anims.create({
            key: 'pacman-chomp',
            frames: [
                { key: 'pacman-open' },
                { key: 'pacman-closed' }
            ],
            frameRate: 8,
            repeat: -1
        });
        
        this.sprite.play('pacman-chomp');
    }
    
    setSpawnPosition(lat, lng) {
        this.spawnPosition = [lat, lng];
        this.currentPosition = [lat, lng];
        this.updateSpritePosition();
        this.sprite.setVisible(true);

        console.log('Pacman 生成位置:', lat, lng);
        console.log('Pacman 像素位置:', this.sprite.x, this.sprite.y);
    }
    
    updateSpritePosition() {
        if (!this.currentPosition || !this.scene.mapManager) return;

        const mapManager = this.scene.mapManager;
        const pixelPos = mapManager.latLngToPixel(this.currentPosition[0], this.currentPosition[1]);

        this.sprite.setPosition(pixelPos.x, pixelPos.y);
    }
    
    setDirection(direction) {
        console.log('setDirection 被調用:', direction, 'canMove:', gameState.canMove, 'autoMoving:', this.autoMoving);

        if (!gameState.canMove || gameState.isPaused || gameState.isGameOver) {
            console.log('無法移動，狀態檢查失敗');
            return;
        }

        console.log('Pacman 設定方向:', direction);
        this.nextDirection = direction;

        // 如果沒有在自動移動，開始自動移動
        if (!this.autoMoving) {
            console.log('開始啟動自動移動');
            this.startAutoMovement();
        } else {
            console.log('已在自動移動中，更新方向');
        }
    }

    startAutoMovement() {
        if (this.autoMoving) return;

        this.autoMoving = true;
        console.log('開始自動移動');
        this.continueMovement();
    }

    stopAutoMovement() {
        this.autoMoving = false;
        if (this.moveTween) {
            this.moveTween.stop();
        }
        this.isMoving = false;
        console.log('停止自動移動');
    }

    continueMovement() {
        console.log('continueMovement 被調用, autoMoving:', this.autoMoving);

        if (!this.autoMoving || gameState.isPaused || gameState.isGameOver) {
            console.log('continueMovement 停止，條件不滿足');
            return;
        }

        // 使用下一個方向或當前方向
        const direction = this.nextDirection || this.currentDirection;
        this.nextDirection = null;

        console.log('嘗試移動方向:', direction);

        if (this.tryMove(direction)) {
            // 移動成功，更新當前方向
            this.currentDirection = direction;
            console.log('移動成功，當前方向:', this.currentDirection);
        } else {
            // 移動失敗，停止自動移動
            console.log('移動失敗，停止自動移動');
            this.stopAutoMovement();
        }
    }
    
    tryMove(direction) {
        if (!this.currentPosition || !this.scene.mapManager) return false;

        const mapManager = this.scene.mapManager;

        // 計算目標位置
        const targetPos = this.calculateTargetPosition(direction);
        if (!targetPos) return false;

        // 檢查目標位置是否有效
        const nearestValidPos = mapManager.findNearestRoadPosition(targetPos[0], targetPos[1]);
        if (!nearestValidPos) return false;

        // 檢查距離是否合理 (避免跳躍太遠)
        const distance = mapManager.calculateLatLngDistance(this.currentPosition, nearestValidPos);
        if (distance > 0.0002) return false; // 約 20 公尺限制

        // 開始移動
        this.startMovement(nearestValidPos, direction);
        return true;
    }
    
    calculateTargetPosition(direction) {
        if (!this.currentPosition) return null;

        const stepSize = 0.00008; // 約 8 公尺，更小的步長讓移動更平滑
        let targetLat = this.currentPosition[0];
        let targetLng = this.currentPosition[1];

        switch (direction) {
            case 'up':
                targetLat += stepSize;
                break;
            case 'down':
                targetLat -= stepSize;
                break;
            case 'left':
                targetLng -= stepSize;
                break;
            case 'right':
                targetLng += stepSize;
                break;
            default:
                return null;
        }

        return [targetLat, targetLng];
    }
    
    startMovement(targetPosition, direction) {
        if (this.isMoving) return;
        
        this.isMoving = true;
        this.targetPosition = targetPosition;
        this.currentDirection = direction;
        
        // 更新 Pacman 朝向
        this.updateRotation(direction);
        
        // 計算移動距離和時間
        const mapManager = this.scene.mapManager;
        const distance = mapManager.calculateLatLngDistance(this.currentPosition, targetPosition);
        const pixelDistance = distance * 1000000; // 轉換為像素距離 (粗略)
        const duration = (pixelDistance / this.speed) * 1000; // 毫秒
        
        // 建立移動補間動畫
        const startPixel = mapManager.latLngToPixel(this.currentPosition[0], this.currentPosition[1]);
        const endPixel = mapManager.latLngToPixel(targetPosition[0], targetPosition[1]);
        
        this.moveTween = this.scene.tweens.add({
            targets: this.sprite,
            x: endPixel.x,
            y: endPixel.y,
            duration: Math.max(100, duration),
            ease: 'Linear',
            onComplete: () => {
                this.onMovementComplete();
            }
        });
    }
    
    onMovementComplete() {
        this.isMoving = false;
        this.currentPosition = this.targetPosition;
        this.targetPosition = null;

        // 如果正在自動移動，繼續移動
        if (this.autoMoving) {
            // 短暫延遲後繼續移動，讓移動更平滑
            setTimeout(() => {
                this.continueMovement();
            }, 50);
        }
    }
    
    updateRotation(direction) {
        let targetRotation = 0;
        
        switch (direction) {
            case 'right':
                targetRotation = 0;
                break;
            case 'down':
                targetRotation = 90;
                break;
            case 'left':
                targetRotation = 180;
                break;
            case 'up':
                targetRotation = 270;
                break;
        }
        
        // 平滑旋轉
        if (this.rotationTween) {
            this.rotationTween.stop();
        }
        
        this.rotationTween = this.scene.tweens.add({
            targets: this.sprite,
            angle: targetRotation,
            duration: 100,
            ease: 'Power2'
        });
    }
    
    update(time, delta) {
        // 更新位置 (如果地圖有變化)
        if (this.currentPosition && !this.isMoving) {
            this.updateSpritePosition();
        }
    }
    
    respawn() {
        if (!this.spawnPosition) return;

        // 停止所有動畫和移動
        this.stopAutoMovement();
        if (this.rotationTween) {
            this.rotationTween.stop();
        }

        // 重置狀態
        this.isMoving = false;
        this.targetPosition = null;
        this.nextDirection = null;
        this.currentDirection = 'left';
        this.autoMoving = false;

        // 回到生成位置
        this.currentPosition = [...this.spawnPosition];
        this.updateSpritePosition();
        this.updateRotation('left');

        // 重新啟用
        this.sprite.setVisible(true);
        this.sprite.setAlpha(1);

        console.log('Pacman 重生');
    }
    
    hide() {
        this.sprite.setVisible(false);
    }
    
    show() {
        this.sprite.setVisible(true);
    }
    
    setAlpha(alpha) {
        this.sprite.setAlpha(alpha);
    }
    
    getCurrentPixelPosition() {
        return {
            x: this.sprite.x,
            y: this.sprite.y
        };
    }
    
    getCurrentLatLngPosition() {
        return this.currentPosition ? [...this.currentPosition] : null;
    }
    
    stop() {
        if (this.moveTween) {
            this.moveTween.stop();
        }
        this.isMoving = false;
        this.nextDirection = null;
    }
    
    destroy() {
        if (this.moveTween) {
            this.moveTween.stop();
        }
        if (this.rotationTween) {
            this.rotationTween.stop();
        }
        if (this.sprite) {
            this.sprite.destroy();
        }
    }
}
