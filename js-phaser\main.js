// Phaser.js 版本的主要入口檔案

import { gameState, GAME_CONFIG, resetGameState, gameEvents } from './gameState.js';
import { GameScene } from './GameScene.js';
import { MapManager } from './MapManager.js';

class PacMapGame {
    constructor() {
        this.phaserGame = null;
        this.mapManager = null;
        this.isInitialized = false;
        
        this.init();
    }
    
    init() {
        console.log('初始化 Pac-Map Phaser.js 版本');
        
        // 設定 Phaser 遊戲配置
        const config = {
            type: Phaser.WEBGL,
            width: '100%',
            height: '100%',
            parent: 'phaser-game',
            backgroundColor: 'transparent',
            transparent: true,
            scale: {
                mode: Phaser.Scale.RESIZE,
                autoCenter: Phaser.Scale.CENTER_BOTH
            },
            physics: {
                default: 'arcade',
                arcade: {
                    gravity: { y: 0 },
                    debug: false
                }
            },
            scene: [GameScene],
            render: {
                antialias: true,
                pixelArt: false,
                transparent: true
            }
        };
        
        // 建立 Phaser 遊戲實例
        this.phaserGame = new Phaser.Game(config);
        gameState.phaserGame = this.phaserGame;
        
        // 設定事件監聽
        this.setupEventListeners();
        
        // 設定 UI 事件
        this.setupUIEvents();
        
        this.isInitialized = true;
        console.log('Phaser 遊戲初始化完成');
    }
    
    setupEventListeners() {
        // 監聽 Phaser 遊戲事件
        this.phaserGame.events.on('ready', () => {
            console.log('Phaser 遊戲準備就緒');
        });
        
        // 監聽遊戲自定義事件
        gameEvents.on('game-start', this.onGameStart, this);
        gameEvents.on('game-over', this.onGameOver, this);
        gameEvents.on('level-complete', this.onLevelComplete, this);
        
        // 監聽視窗大小變化
        window.addEventListener('resize', () => {
            if (this.phaserGame) {
                this.phaserGame.scale.refresh();
            }
        });
    }
    
    setupUIEvents() {
        // 開始遊戲按鈕
        const startBtn = document.getElementById('startGameBtn');
        if (startBtn) {
            startBtn.addEventListener('click', () => {
                this.startGame();
            });
        }
        
        // 說明按鈕
        const instructionsBtn = document.getElementById('instructionsBtn');
        if (instructionsBtn) {
            instructionsBtn.addEventListener('click', () => {
                this.showInstructions();
            });
        }
        
        // 鍵盤事件 (全域)
        document.addEventListener('keydown', (event) => {
            this.handleGlobalKeyDown(event);
        });
    }
    
    handleGlobalKeyDown(event) {
        // 處理全域按鍵 (如暫停、開發者控制台等)
        if (event.code === 'Escape') {
            event.preventDefault();
            this.togglePause();
        }
        
        // 其他按鍵由 GameScene 處理
    }
    
    async startGame() {
        console.log('開始遊戲');

        try {
            // 隱藏開始畫面
            this.hideStartScreen();

            // 顯示載入畫面
            this.showLoadingScreen('正在初始化遊戲...');

            // 重置遊戲狀態
            resetGameState();

            // 等待 Phaser 場景準備就緒
            const gameScene = this.phaserGame.scene.getScene('GameScene');
            if (!gameScene) {
                throw new Error('找不到遊戲場景');
            }

            // 等待場景完全載入
            await new Promise(resolve => {
                if (gameScene.scene.isActive()) {
                    resolve();
                } else {
                    gameScene.scene.start();
                    gameScene.events.once('create', resolve);
                }
            });

            // 檢查地圖容器
            const mapContainer = document.getElementById('map');
            console.log('地圖容器狀態:', {
                exists: !!mapContainer,
                visible: mapContainer ? mapContainer.offsetWidth > 0 : false,
                dimensions: mapContainer ? `${mapContainer.offsetWidth}x${mapContainer.offsetHeight}` : 'N/A'
            });

            // 初始化地圖管理器
            this.mapManager = new MapManager(gameScene);

            // 載入地圖
            const mapLoaded = await this.mapManager.initializeMap(gameState.currentMapIndex);
            if (!mapLoaded) {
                throw new Error('地圖載入失敗');
            }

            // 初始化遊戲元素
            await this.initializeGameElements();

            // 隱藏載入畫面
            this.hideLoadingScreen();

            // 顯示遊戲 UI
            this.showGameUI();

            // 啟動遊戲
            gameState.canMove = true;
            gameState.gameStartTime = performance.now();

            // 發送遊戲開始事件
            gameEvents.emit('game-start');

            console.log('遊戲啟動成功');

        } catch (error) {
            console.error('遊戲啟動失敗:', error);
            this.showError('遊戲啟動失敗，請重試');
        }
    }
    
    async initializeGameElements() {
        const gameScene = this.phaserGame.scene.getScene('GameScene');
        if (!gameScene) {
            throw new Error('找不到遊戲場景');
        }

        // 初始化遊戲場景的元素
        gameScene.initializeGameElements(this.mapManager);

        // 生成 Pacman 起始位置
        const spawnPoints = this.mapManager.getSpawnPoints(1);
        if (spawnPoints.length === 0) {
            throw new Error('找不到有效的生成位置');
        }

        // 設定 Pacman 位置
        if (gameScene.pacmanPlayer) {
            gameScene.pacmanPlayer.setSpawnPosition(spawnPoints[0][0], spawnPoints[0][1]);
        }

        // 生成鬼怪
        if (gameScene.ghostManager) {
            const ghostSpawnPoints = this.mapManager.getSpawnPoints(GAME_CONFIG.NUMBER_OF_GHOSTS);
            gameScene.ghostManager.spawnGhosts(ghostSpawnPoints);
        }

        // 生成食物
        if (gameScene.itemManager) {
            gameScene.itemManager.generateItems();
        }

        console.log('遊戲元素初始化完成');
    }
    
    togglePause() {
        if (gameState.isGameOver) return;
        
        gameState.isPaused = !gameState.isPaused;
        
        const gameScene = this.phaserGame.scene.getScene('GameScene');
        if (gameScene) {
            if (gameState.isPaused) {
                gameScene.scene.pause();
                this.showPauseScreen();
            } else {
                gameScene.scene.resume();
                this.hidePauseScreen();
            }
        }
    }
    
    restartGame() {
        console.log('重新開始遊戲');
        
        // 清理當前遊戲
        this.cleanup();
        
        // 重新開始
        this.startGame();
    }
    
    endGame() {
        console.log('結束遊戲');
        
        gameState.isGameOver = true;
        gameState.canMove = false;
        
        // 顯示遊戲結束畫面
        this.showGameOverScreen();
        
        // 清理資源
        this.cleanup();
    }
    
    cleanup() {
        // 清理地圖管理器
        if (this.mapManager) {
            this.mapManager.destroy();
            this.mapManager = null;
        }
        
        // 清理遊戲狀態
        resetGameState();
    }
    
    // UI 控制方法
    hideStartScreen() {
        const startScreen = document.getElementById('startScreen');
        if (startScreen) {
            startScreen.classList.add('hidden');
        }
    }
    
    showStartScreen() {
        const startScreen = document.getElementById('startScreen');
        if (startScreen) {
            startScreen.classList.remove('hidden');
        }
    }
    
    showGameUI() {
        const gameUI = document.getElementById('gameUI');
        if (gameUI) {
            gameUI.classList.remove('hidden');
        }
    }
    
    hideGameUI() {
        const gameUI = document.getElementById('gameUI');
        if (gameUI) {
            gameUI.classList.add('hidden');
        }
    }
    
    showPauseScreen() {
        // 實現暫停畫面
        console.log('顯示暫停畫面');
    }
    
    hidePauseScreen() {
        // 隱藏暫停畫面
        console.log('隱藏暫停畫面');
    }
    
    showGameOverScreen() {
        // 實現遊戲結束畫面
        console.log('顯示遊戲結束畫面');
        this.hideGameUI();
        this.showStartScreen();
    }
    
    showInstructions() {
        // 實現說明畫面
        console.log('顯示遊戲說明');
    }
    
    showLoadingScreen(message = '正在載入...') {
        const loadingScreen = document.getElementById('loadingScreen');
        const loadingText = document.getElementById('loadingText');

        if (loadingScreen && loadingText) {
            loadingText.textContent = message;
            loadingScreen.classList.remove('hidden');
        }
    }

    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.classList.add('hidden');
        }
    }

    showError(message) {
        this.showLoadingScreen(message);

        // 3 秒後隱藏錯誤訊息
        setTimeout(() => {
            this.hideLoadingScreen();
            this.showStartScreen();
        }, 3000);
    }
    
    // 事件處理器
    onGameStart() {
        console.log('遊戲開始事件');
    }
    
    onGameOver(data) {
        console.log('遊戲結束事件:', data);
        this.endGame();
    }
    
    onLevelComplete() {
        console.log('關卡完成事件');
    }
    
    // 銷毀方法
    destroy() {
        this.cleanup();
        
        if (this.phaserGame) {
            this.phaserGame.destroy(true);
            this.phaserGame = null;
        }
    }
}

// 當 DOM 載入完成時啟動遊戲
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM 載入完成，啟動 Pac-Map');
    
    // 建立遊戲實例
    window.pacMapGame = new PacMapGame();
    
    console.log('Pac-Map Phaser.js 版本載入完成！');
});
